{"name": "@yeepay/coderocket-mcp", "version": "1.4.0", "description": "CodeRocket MCP - Independent AI-powered code review server for Model Context Protocol", "main": "dist/index.js", "type": "module", "bin": {"coderocket-mcp": "./bin/coderocket-mcp"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "node dist/test.js", "test:mcp": "npm run build && node bin/coderocket-mcp test", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepare": "npm run build", "prepublishOnly": "npm run build", "preversion": "npm run build", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags"}, "keywords": ["mcp", "model-context-protocol", "code-review", "ai", "gemini", "claudecode", "typescript", "code-analysis", "git", "programming-tools", "automation", "claude", "ai-tools", "independent", "standalone", "no-dependencies"], "author": "dreambt <<EMAIL>>", "license": "Apache-2.0", "dependencies": {"@anthropic-ai/sdk": "^0.27.0", "@google/generative-ai": "^0.21.0", "@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.24.6"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.1", "globals": "^16.3.0", "prettier": "^3.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.38.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/im47cn/coderocket-mcp.git", "directory": "coderocket-mcp"}, "bugs": {"url": "https://github.com/im47cn/coderocket-mcp/issues"}, "homepage": "https://github.com/im47cn/coderocket-mcp#readme"}